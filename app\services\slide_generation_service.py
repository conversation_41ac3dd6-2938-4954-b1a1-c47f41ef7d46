"""
Slide Generation Service
Xử lý logic sinh nội dung slide từ lesson content và template structure sử dụng LLM
"""

import logging
import threading
import json
import re
from typing import Dict, Any, List, Optional
from datetime import datetime

from app.services.llm_service import get_llm_service
from app.services.textbook_retrieval_service import TextbookRetrievalService
from app.services.google_slides_service import get_google_slides_service

logger = logging.getLogger(__name__)


class SlideGenerationService:
    """
    Service để sinh nội dung slide từ lesson content và template
    Singleton pattern với Lazy Initialization
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """Singleton pattern implementation với thread-safe"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(SlideGenerationService, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Lazy initialization - chỉ khởi tạo một lần"""
        if self._initialized:
            return

        self.llm_service = None
        self.textbook_service = None
        self.slides_service = None
        self._service_initialized = False
        self._initialized = True

    def _ensure_service_initialized(self):
        """Ensure services are initialized"""
        if not self._service_initialized:
            logger.info("🔄 SlideGenerationService: First-time initialization triggered")
            self.llm_service = get_llm_service()
            self.textbook_service = TextbookRetrievalService()
            self.slides_service = get_google_slides_service()
            self._service_initialized = True
            logger.info("✅ SlideGenerationService: Initialization completed")

    def is_available(self) -> bool:
        """Kiểm tra service có sẵn sàng không"""
        self._ensure_service_initialized()
        return (self.llm_service and self.llm_service.is_available() and 
                self.slides_service and self.slides_service.is_available())

    async def generate_slides_from_lesson(
        self,
        lesson_id: str,
        template_id: str,
        config_prompt: Optional[str] = None,
        presentation_title: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Tạo slides từ lesson_id và template_id (QUY TRÌNH MỚI)

        Args:
            lesson_id: ID của bài học
            template_id: ID của Google Slides template
            config_prompt: Prompt cấu hình tùy chỉnh (optional)
            presentation_title: Tiêu đề presentation tùy chỉnh (optional)

        Returns:
            Dict chứa kết quả tạo slides
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Slide generation service not available"
            }

        try:
            logger.info(f"Starting NEW slide generation process for lesson {lesson_id} with template {template_id}")

            # Bước 1: Lấy nội dung bài học
            lesson_result = await self.textbook_service.get_lesson_content(lesson_id)
            if not lesson_result:
                return {
                    "success": False,
                    "error": f"Could not retrieve lesson content for {lesson_id}"
                }

            lesson_content = lesson_result.get("lesson_content", "")
            if not lesson_content:
                return {
                    "success": False,
                    "error": f"Empty lesson content for {lesson_id}"
                }

            # Bước 2: Copy template và phân tích cấu trúc của bản sao (QUY TRÌNH MỚI)
            new_title = presentation_title or f"Bài học {lesson_id} - {datetime.now().strftime('%Y%m%d_%H%M%S')}"
            copy_and_analyze_result = await self.slides_service.copy_and_analyze_template(template_id, new_title)
            if not copy_and_analyze_result["success"]:
                return {
                    "success": False,
                    "error": f"Could not copy and analyze template: {copy_and_analyze_result['error']}"
                }

            # Bước 3: Sinh nội dung slides bằng LLM với cấu trúc của bản sao
            slides_content = await self._generate_slides_content(
                lesson_content,
                copy_and_analyze_result,
                config_prompt
            )
            if not slides_content["success"]:
                return slides_content

            # Bước 4: Cập nhật nội dung vào bản sao đã tạo
            update_result = await self.slides_service.update_copied_presentation_content(
                copy_and_analyze_result["copied_presentation_id"],
                slides_content["slides"]
            )
            if not update_result["success"]:
                return {
                    "success": False,
                    "error": f"Could not update presentation content: {update_result['error']}"
                }

            # Bước 5: Xóa slides thừa (không được sử dụng)
            used_slide_ids = []
            for slide_data in slides_content["slides"]:
                slide_id = slide_data.get('slideId')
                if slide_id and not slide_id.startswith('new_slide_'):
                    used_slide_ids.append(slide_id)

            if used_slide_ids:
                delete_result = await self.slides_service.delete_unused_slides(
                    copy_and_analyze_result["copied_presentation_id"],
                    used_slide_ids
                )
                logger.info(f"Unused slides cleanup: {delete_result}")
            else:
                logger.info("No slide cleanup needed - all slides are new")

            return {
                "success": True,
                "lesson_id": lesson_id,
                "original_template_id": template_id,
                "presentation_id": copy_and_analyze_result["copied_presentation_id"],
                "presentation_title": copy_and_analyze_result["presentation_title"],
                "web_view_link": copy_and_analyze_result["web_view_link"],
                "slides_created": update_result.get("slides_updated", 0) + update_result.get("slides_created", 0),
                "template_info": {
                    "title": copy_and_analyze_result["presentation_title"],
                    "layouts_count": copy_and_analyze_result["slide_count"]
                }
            }

        except Exception as e:
            logger.error(f"Error generating slides: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _generate_slides_content(
        self,
        lesson_content: str,
        copied_presentation_info: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Sinh nội dung slides bằng LLM theo quy trình 2 lần gọi AI mới

        Args:
            lesson_content: Nội dung bài học
            copied_presentation_info: Thông tin presentation đã copy và phân tích
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            Dict chứa nội dung slides đã sinh
        """
        try:
            # Bước 1: Phân tích template và thêm placeholder types
            logger.info("🔍 Step 1: Analyzing template and detecting placeholder types...")
            analyzed_template = self._analyze_template_with_placeholders(copied_presentation_info)

            # Bước 2: Lần 1 gọi AI - Sinh presentation-content từ lesson_content
            logger.info("🤖 Step 2: First AI call - Generate presentation content...")
            presentation_content = await self._generate_presentation_content(lesson_content, config_prompt)
            if not presentation_content["success"]:
                return presentation_content

            # Bước 3: Lần 2 gọi AI - Map presentation-content vào template
            logger.info("🎯 Step 3: Second AI call - Map content to template...")
            mapped_slides = await self._map_content_to_template(
                presentation_content["content"],
                analyzed_template,
                config_prompt
            )
            if not mapped_slides["success"]:
                return mapped_slides

            # Bước 4: Lọc và chỉ giữ slides được sử dụng
            logger.info("🧹 Step 4: Filter and keep only used slides...")
            final_slides = self._filter_used_slides(mapped_slides["slides"])

            return {
                "success": True,
                "slides": final_slides,
                "presentation_content": presentation_content["content"],  # For debugging
                "analyzed_template": analyzed_template  # For debugging
            }

        except Exception as e:
            logger.error(f"Error generating slides content: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _analyze_template_with_placeholders(self, copied_presentation_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Phân tích template và thêm placeholder types theo enum yêu cầu

        Args:
            copied_presentation_info: Thông tin presentation đã copy

        Returns:
            Dict chứa template đã phân tích với placeholder types
        """
        try:
            analyzed_slides = []

            for slide in copied_presentation_info.get("slides", []):
                analyzed_elements = []
                placeholder_counts = {}

                for element in slide.get("elements", []):
                    text = element.get("text", "").strip()

                    if text:  # Chỉ xử lý elements có text
                        logger.info(f"🔍 Processing text in slide {slide.get('slideId')}: '{text}'")

                        # Detect placeholder type và max_length từ text
                        placeholder_result = self._detect_placeholder_type_from_text(text)

                        if placeholder_result:  # Chỉ xử lý nếu detect được placeholder
                            placeholder_type, max_length = placeholder_result

                            logger.info(f"✅ Found placeholder: {placeholder_type} <{max_length}>")

                            # Đếm số lượng placeholder types
                            placeholder_counts[placeholder_type] = placeholder_counts.get(placeholder_type, 0) + 1

                            # Tạo analyzed element với thông tin đầy đủ
                            analyzed_element = {
                                "objectId": element.get("objectId"),
                                "text": None,  # LLM sẽ insert nội dung sau
                                "Type": placeholder_type,
                                "max_length": max_length,
                            }

                            analyzed_elements.append(analyzed_element)
                        else:
                            # Bỏ qua text không phải placeholder format
                            logger.info(f"❌ Skipping non-placeholder text: '{text}'")
                            continue

                # Tạo description cho slide dựa trên placeholder counts
                description = self._generate_slide_description(placeholder_counts)

                analyzed_slide = {
                    "slideId": slide.get("slideId"),
                    "description": description,
                    "elements": analyzed_elements,
                    "placeholder_counts": placeholder_counts  # For logic selection
                }

                analyzed_slides.append(analyzed_slide)

            return {
                "slides": analyzed_slides,
                "total_slides": len(analyzed_slides),
                "original_info": copied_presentation_info
            }

        except Exception as e:
            logger.error(f"Error analyzing template with placeholders: {e}")
            return {"slides": [], "total_slides": 0, "original_info": copied_presentation_info}

    def _detect_placeholder_type_from_text(self, text: str) -> Optional[tuple]:
        """
        Detect placeholder type và max_length từ text format "PlaceholderName <max_length>"

        Args:
            text: Text từ element

        Returns:
            tuple: (placeholder_type, max_length) hoặc None nếu không detect được
        """
        try:
            # Tìm pattern "PlaceholderName max_length" (không có dấu < >)
            pattern = r'(\w+)\s+(\d+)'
            match = re.search(pattern, text)

            if match:
                placeholder_name = match.group(1)
                max_length = int(match.group(2))

                # Map placeholder name to enum
                placeholder_type = self._map_to_placeholder_enum(placeholder_name)
                if placeholder_type:  # Chỉ return nếu tìm thấy valid placeholder
                    return placeholder_type, max_length

            return None

        except Exception as e:
            logger.warning(f"Error detecting placeholder type: {e}")
            return None

    def _map_to_placeholder_enum(self, placeholder_name: str) -> Optional[str]:
        """
        Map placeholder name to enum values

        Args:
            placeholder_name: Name from text

        Returns:
            str: Enum placeholder type
        """
        # Mapping dictionary
        mapping = {
            "LessonName": "LessonName",
            "LessonDescription": "LessonDescription",
            "CreatedDate": "CreatedDate",
            "TitleName": "TitleName",
            "TitleContent": "TitleContent",
            "SubtitleName": "SubtitleName",
            "SubtitleContent": "SubtitleContent",
            "BulletItem": "BulletItem",
            "ImageName": "ImageName",
            "ImageContent": "ImageContent"
        }

        return mapping.get(placeholder_name)  # Return None if not found


    def _generate_slide_description(self, placeholder_counts: Dict[str, int]) -> str:
        """
        Generate description for slide based on placeholder counts

        Args:
            placeholder_counts: Dictionary of placeholder type counts

        Returns:
            str: Generated description
        """
        try:
            if not placeholder_counts:
                return "Slide trống"

            descriptions = []
            for placeholder_type, count in placeholder_counts.items():
                if count > 0:
                    if count == 1:
                        descriptions.append(f"1 {placeholder_type}")
                    else:
                        descriptions.append(f"{count} {placeholder_type}")

            if descriptions:
                return f"Slide dành cho {', '.join(descriptions)}"
            else:
                return "Slide trống"

        except Exception as e:
            logger.warning(f"Error generating slide description: {e}")
            return "Slide không xác định"

    async def _generate_presentation_content(self, lesson_content: str, config_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        Lần 1 gọi AI: Sinh presentation-content từ lesson_content

        Args:
            lesson_content: Nội dung bài học
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            Dict chứa presentation content đã sinh
        """
        try:
            # Tạo prompt cho lần gọi AI đầu tiên
            prompt = self._create_presentation_content_prompt(lesson_content, config_prompt)

            logger.info(f"First AI call prompt length: {len(prompt)} characters")

            # Gọi LLM với retry logic
            max_retries = 3
            for attempt in range(max_retries):
                logger.info(f"First AI call attempt {attempt + 1}/{max_retries}")

                llm_result = await self.llm_service._generate_content(prompt)

                if llm_result["success"] and llm_result.get("text") and llm_result["text"].strip():
                    logger.info(f"First AI call successful on attempt {attempt + 1}")

                    # Simply return the text content (no JSON parsing needed)
                    presentation_content = llm_result["text"].strip()
                    logger.debug(f"First AI response length: {len(presentation_content)} characters")
                    logger.debug(f"First AI response preview: {presentation_content[:200]}...")

                    return {
                        "success": True,
                        "content": presentation_content
                    }
                else:
                    logger.warning(f"First AI call attempt {attempt + 1} failed: {llm_result.get('error', 'Empty response')}")
                    if attempt == max_retries - 1:
                        return {
                            "success": False,
                            "error": f"First AI call failed after {max_retries} attempts: {llm_result.get('error', 'Empty response')}"
                        }

                # Wait before retry
                import asyncio
                await asyncio.sleep(1)

            return {
                "success": False,
                "error": "First AI call failed"
            }

        except Exception as e:
            logger.error(f"Error in first AI call: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _create_presentation_content_prompt(self, lesson_content: str, config_prompt: Optional[str] = None) -> str:
        """
        Tạo prompt cho lần gọi AI đầu tiên - sinh presentation content

        Args:
            lesson_content: Nội dung bài học
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            str: Prompt cho AI
        """
        default_config = """
Bạn là chuyên gia thiết kế nội dung thuyết trình giáo dục. Nhiệm vụ của bạn là phân tích nội dung bài học và tạo ra nội dung thuyết trình đầy đủ, có cấu trúc logic.

NGUYÊN TẮC THIẾT KẾ:
1. PHÂN TÍCH TOÀN DIỆN - Hiểu rõ nội dung bài học và chia thành các phần logic
2. CẤU TRÚC RÕ RÀNG - Từ tổng quan đến chi tiết, có thứ tự logic
3. NỘI DUNG PHONG PHÚ - Đủ chi tiết để tạo thành presentation hoàn chỉnh
4. PHÂN LOẠI THÔNG TIN - Chia rõ tiêu đề, nội dung, bullet points
5. KÝ HIỆU KHOA HỌC CHÍNH XÁC - Sử dụng Unicode cho công thức

YÊU CẦU CỤ THỂ:
- Tạo nội dung thuyết trình đầy đủ từ lesson content
- Chia thành các phần có cấu trúc: LessonName, LessonDescription, TitleName, TitleContent, SubtitleName, SubtitleContent, BulletItem
- Mỗi phần có nội dung chi tiết, phù hợp để trình bày
- Sử dụng ký hiệu khoa học chính xác: H₂O, CO₂, x², √x, π, α, β
- Nội dung phải logic, dễ hiểu và phù hợp với đối tượng học sinh
"""

        final_config = config_prompt if config_prompt else default_config

        prompt = f"""
{final_config}

NỘI DUNG BÀI HỌC:
{lesson_content}

HƯỚNG DẪN TẠO PRESENTATION CONTENT:

1. PHÂN TÍCH BÀI HỌC:
   - Xác định chủ đề chính và các chủ đề phụ
   - Chia nội dung thành các phần logic
   - Xác định thông tin quan trọng cần nhấn mạnh

2. TẠO CẤU TRÚC PRESENTATION:
   - LessonName: Tên bài học ngắn gọn, súc tích
   - LessonDescription: Tóm tắt bài học (2-3 câu)
   - CreatedDate: Ngày hiện tại
   - Các TitleName: Tên các phần chính của bài học
   - Các TitleContent: Nội dung chi tiết cho từng phần chính
   - Các SubtitleName: Tên các mục nhỏ trong từng phần
   - Các SubtitleContent: Nội dung chi tiết cho từng mục nhỏ
   - BulletItem: Các điểm quan trọng dạng bullet points
   - ImageName: Tên mô tả cho vùng hình ảnh
   - ImageContent: Mô tả nội dung hình ảnh cần thiết

3. YÊU CẦU NỘI DUNG:
   - Nội dung phải đầy đủ, chi tiết
   - Sử dụng ngôn ngữ phù hợp với học sinh
   - Ký hiệu khoa học chính xác
   - Logic trình bày rõ ràng

YÊU CẦU OUTPUT:
Tạo nội dung thuyết trình đầy đủ dưới dạng TEXT thuần túy, có cấu trúc rõ ràng.

FORMAT THUYẾT TRÌNH:

# [TÊN BÀI HỌC]

## Tóm tắt bài học
[Mô tả ngắn gọn về bài học]

## I. [TÊN PHẦN CHÍNH 1]

### Nội dung chính:
[Nội dung chi tiết của phần này]

### 1.1 [Tên mục nhỏ]
[Nội dung chi tiết mục nhỏ]

**Các điểm quan trọng:**
• [Điểm quan trọng 1]
• [Điểm quan trọng 2]
• [Điểm quan trọng 3]

**Hình ảnh minh họa:** [Mô tả hình ảnh cần thiết]

### 1.2 [Tên mục nhỏ khác]
[Nội dung chi tiết mục nhỏ khác]

## II. [TÊN PHẦN CHÍNH 2]
[Tiếp tục theo cấu trúc tương tự]

QUY TẮC VIẾT:
- Sử dụng markdown headers (# ## ###) để phân cấp
- Nội dung đầy đủ, chi tiết, dễ hiểu
- Ký hiệu khoa học chính xác: H₂O, CO₂, x², √x, π, α, β
- Logic trình bày từ tổng quan đến chi tiết
- Bullet points rõ ràng với ký hiệu •
- Mô tả hình ảnh cần thiết cho minh họa
- KHÔNG cần JSON, chỉ cần text thuyết trình thuần túy
"""

        return prompt

    async def _map_content_to_template(
        self,
        presentation_content: str,
        analyzed_template: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Lần 2 gọi AI: Map presentation-content vào template

        Args:
            presentation_content: Nội dung presentation text từ lần gọi AI đầu tiên
            analyzed_template: Template đã phân tích với placeholder types
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            Dict chứa slides đã map content
        """
        try:
            # Tạo prompt cho lần gọi AI thứ hai
            prompt = self._create_content_mapping_prompt(presentation_content, analyzed_template, config_prompt)

            logger.info(f"Second AI call prompt length: {len(prompt)} characters")
            logger.info("-------------------------------")
            logger.info(presentation_content)
            logger.info("-------------------------------")
            logger.info("-------------------------------")
            logger.info(analyzed_template)
            logger.info("-------------------------------")
            # Gọi LLM với retry logic
            max_retries = 3
            for attempt in range(max_retries):
                logger.info(f"Second AI call attempt {attempt + 1}/{max_retries}")

                llm_result = await self.llm_service._generate_content(prompt)

                if llm_result["success"] and llm_result.get("text") and llm_result["text"].strip():
                    logger.info(f"Second AI call successful on attempt {attempt + 1}")
                    logger.info("-------------------------------")
                    logger.info(llm_result)
                    logger.info("-------------------------------")
                    # Parse JSON response with improved error handling
                    try:
                        response_text = llm_result["text"].strip()
                        logger.debug(f"Raw second AI response: {response_text[:500]}...")

                        # Try multiple JSON extraction methods
                        mapped_slides = None

                        # Method 1: Look for JSON array
                        json_start = response_text.find('[')
                        json_end = response_text.rfind(']') + 1

                        if json_start != -1 and json_end > json_start:
                            json_text = response_text[json_start:json_end]
                            try:
                                mapped_slides = json.loads(json_text)
                                logger.info("Successfully parsed JSON using array extraction")
                            except json.JSONDecodeError:
                                logger.warning("Array extraction failed, trying full response")

                        # Method 2: Try full response if array extraction failed
                        if mapped_slides is None:
                            try:
                                mapped_slides = json.loads(response_text)
                                logger.info("Successfully parsed JSON using full response")
                            except json.JSONDecodeError:
                                logger.warning("Full response parsing failed, trying cleanup")

                        # Method 3: Clean up common JSON issues and retry
                        if mapped_slides is None:
                            cleaned_text = self._clean_json_response(response_text)
                            try:
                                mapped_slides = json.loads(cleaned_text)
                                logger.info("Successfully parsed JSON after cleanup")
                            except json.JSONDecodeError as e:
                                logger.error(f"All JSON parsing methods failed: {e}")
                                logger.error(f"Problematic response: {response_text[:1000]}")
                                raise e

                        if not isinstance(mapped_slides, list):
                            raise ValueError("Second AI output must be a list of slides")

                        # Clean up any font_size references that AI might have included
                        cleaned_slides = self._clean_font_references(mapped_slides)

                        return {
                            "success": True,
                            "slides": cleaned_slides
                        }

                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to parse second AI response as JSON: {e}")
                        logger.error(f"Response excerpt: {response_text[:1000] if response_text else 'EMPTY'}")
                        if attempt == max_retries - 1:
                            return {
                                "success": False,
                                "error": f"Invalid JSON from second AI call: {e}"
                            }
                else:
                    logger.warning(f"Second AI call attempt {attempt + 1} failed: {llm_result.get('error', 'Empty response')}")
                    if attempt == max_retries - 1:
                        return {
                            "success": False,
                            "error": f"Second AI call failed after {max_retries} attempts: {llm_result.get('error', 'Empty response')}"
                        }

                # Wait before retry
                import asyncio
                await asyncio.sleep(1)

            return {
                "success": False,
                "error": "Second AI call failed"
            }

        except Exception as e:
            logger.error(f"Error in second AI call: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _create_content_mapping_prompt(
        self,
        presentation_content: str,
        analyzed_template: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> str:
        """
        Tạo prompt cho lần gọi AI thứ hai - map content vào template

        Args:
            presentation_content: Nội dung presentation (text thuần túy)
            analyzed_template: Template đã phân tích
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            str: Prompt cho AI
        """
        default_config = """
Bạn là chuyên gia mapping nội dung vào template slide. Nhiệm vụ của bạn là ghép nội dung thuyết trình vào các placeholder của template một cách chính xác và thông minh.

NGUYÊN TẮC MAPPING:
1. TUÂN THỦ NGHIÊM NGẶT max_length của từng placeholder
2. KHÔNG tự design lại hay thêm format mới
3. CHỈ map nội dung phù hợp với Type của placeholder
4. Nếu nội dung dài hơn max_length thì tự động viết lại cho <= max_length
5. LOGIC chọn slide: chỉ sử dụng slide có đủ placeholder phù hợp với nội dung

YÊU CẦU MAPPING:
- LessonName: Map từ lesson_name
- LessonDescription: Map từ lesson_description
- CreatedDate: Map từ created_date
- TitleName: Map từ title_name của sections
- TitleContent: Map từ title_content của sections
- SubtitleName: Map từ subtitle_name của subsections
- SubtitleContent: Map từ subtitle_content của subsections
- BulletItem: Map từ bullet_items (format với • hoặc -)
- ImageName: Map từ image_name
- ImageContent: Map từ image_content
"""

        final_config = config_prompt if config_prompt else default_config

        # Tạo thông tin chi tiết về template
        template_info = "TEMPLATE STRUCTURE:\n"
        for i, slide in enumerate(analyzed_template.get("slides", [])):
            template_info += f"\nSlide {i+1} (ID: {slide['slideId']}):\n"
            template_info += f"Description: {slide['description']}\n"
            template_info += f"Elements:\n"

            for j, element in enumerate(slide.get("elements", [])):
                template_info += f"  {j+1}. {element['objectId']}: Type={element['Type']}, max_length={element['max_length']}\n"

        # Use presentation content directly (it's already text)
        content_str = presentation_content

        prompt = f"""
{final_config}

PRESENTATION CONTENT (TEXT):
{content_str}

{template_info}

HƯỚNG DẪN MAPPING:

1. PHÂN TÍCH TEXT CONTENT:
   - Đọc và hiểu toàn bộ nội dung thuyết trình text
   - Xác định phần nào trong text map vào placeholder nào
   - Trích xuất thông tin phù hợp từ text content

2. CHỌN SLIDE PHÙ HỢP:
   - Chỉ sử dụng slide có đủ placeholder type cần thiết
   - Ví dụ: Nếu có 1 TitleName và 2 SubtitleName thì chọn slide có đủ các placeholder này
   - KHÔNG sử dụng slide không phù hợp

3. MAP TEXT CONTENT:
   - Từ text thuyết trình, trích xuất thông tin phù hợp cho từng placeholder
   - LessonName: Lấy từ tiêu đề chính (# header)
   - LessonDescription: Lấy từ phần tóm tắt
   - TitleName: Lấy từ các ## headers (phần chính)
   - TitleContent: Lấy nội dung chi tiết dưới ## headers
   - SubtitleName: Lấy từ các ### headers (mục nhỏ)
   - SubtitleContent: Lấy nội dung chi tiết dưới ### headers
   - BulletItem: Lấy từ các bullet points (•) trong text
   - ImageName/ImageContent: Lấy từ phần mô tả hình ảnh
   - Tuân thủ nghiêm ngặt max_length
   - Nếu nội dung dài hơn max_length, viết lại ngắn gọn hơn
   - Giữ nguyên ý nghĩa và thông tin quan trọng
   - Format BulletItem với • hoặc - và \\n

4. LOGIC CHỌN SLIDE:
   - Slide có LessonName, LessonDescription: dùng cho thông tin tổng quan
   - Slide có TitleName, TitleContent: dùng cho các section chính
   - Slide có SubtitleName, SubtitleContent: dùng cho subsection
   - Slide có BulletItem: dùng cho danh sách điểm quan trọng
   - Slide có ImageName, ImageContent: dùng khi cần minh họa

YÊU CẦU OUTPUT:
QUAN TRỌNG: Chỉ trả về JSON array hợp lệ, KHÔNG có text khác.

Format chính xác:
[
  {{
    "slideId": "slide_id_from_template",
    "elements": [
      {{
        "objectId": "element_object_id",
        "text": "Nội dung đã map (tuân thủ max_length)",
        "Type": "PlaceholderType",
        "max_length": 50
      }}
    ]
  }}
]

QUY TẮC JSON TUYỆT ĐỐI:
- Bắt đầu bằng [ và kết thúc bằng ]
- Mỗi object phải có dấu phẩy giữa các elements (trừ element cuối)
- String values phải được bao bởi dấu ngoặc kép
- Boolean values: true/false (không có dấu ngoặc kép)
- Number values: không có dấu ngoặc kép
- KHÔNG có trailing comma trước dấu đóng ngoặc
- CHỈ sử dụng slideId và objectId có trong template
- TUÂN THỦ NGHIÊM NGẶT max_length
- KHÔNG text giải thích, chỉ JSON
- Giữ nguyên metadata: Type, max_length

VÍ DỤ JSON ĐÚNG:
[
  {{
    "slideId": "slide1",
    "elements": [
      {{
        "objectId": "obj1",
        "text": "Bài học Toán",
        "Type": "LessonName",
        "max_length": 50
      }}
    ]
  }}
]
"""

        return prompt

    def _filter_used_slides(self, mapped_slides: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Lọc và chỉ giữ slides được sử dụng, xóa slides thừa

        Args:
            mapped_slides: Slides đã map content từ AI

        Returns:
            List slides đã lọc (chỉ giữ slides được sử dụng)
        """
        try:
            used_slide_ids = set()
            final_slides = []

            # Collect used slide IDs
            for slide in mapped_slides:
                slide_id = slide.get("slideId")
                if slide_id and slide.get("elements"):
                    used_slide_ids.add(slide_id)

            logger.info(f"Used slide IDs: {list(used_slide_ids)}")

            # Convert mapped slides to final format (compatible with Google Slides API)
            for slide in mapped_slides:
                slide_id = slide.get("slideId")
                elements = slide.get("elements", [])

                if slide_id and elements:
                    # Create updates dictionary for Google Slides API
                    updates = {}
                    for element in elements:
                        object_id = element.get("objectId")
                        text = element.get("text")
                        if object_id and text is not None:
                            updates[object_id] = text

                    if updates:
                        final_slide = {
                            "slideId": slide_id,
                            "action": "update",
                            "updates": updates
                        }
                        final_slides.append(final_slide)

            logger.info(f"Final slides count: {len(final_slides)}")
            return final_slides

        except Exception as e:
            logger.error(f"Error filtering used slides: {e}")
            return mapped_slides  # Return original as fallback

    def _clean_json_response(self, response_text: str) -> str:
        """
        Clean up common JSON formatting issues in AI responses

        Args:
            response_text: Raw response from AI

        Returns:
            str: Cleaned JSON text
        """
        try:
            # Remove common prefixes/suffixes
            cleaned = response_text.strip()

            # Remove markdown code blocks
            if cleaned.startswith('```json'):
                cleaned = cleaned[7:].strip()
            elif cleaned.startswith('```'):
                cleaned = cleaned[3:].strip()
            if cleaned.endswith('```'):
                cleaned = cleaned[:-3].strip()

            # Extract JSON content between first { or [ and last } or ]
            start_chars = ['{', '[']

            start_pos = -1
            start_char = None
            for char in start_chars:
                pos = cleaned.find(char)
                if pos != -1 and (start_pos == -1 or pos < start_pos):
                    start_pos = pos
                    start_char = char

            if start_pos != -1:
                # Find matching end character
                end_char = '}' if start_char == '{' else ']'
                end_pos = cleaned.rfind(end_char)

                if end_pos != -1 and end_pos > start_pos:
                    cleaned = cleaned[start_pos:end_pos + 1]

            # Fix common JSON issues with regex
            import re

            # Remove trailing commas before closing brackets/braces
            cleaned = re.sub(r',(\s*[}\]])', r'\1', cleaned)

            # Fix missing commas between objects/arrays
            cleaned = re.sub(r'}\s*{', r'},{', cleaned)
            cleaned = re.sub(r']\s*\[', r'],[', cleaned)

            # Fix missing commas after closing braces/brackets when followed by opening ones
            cleaned = re.sub(r'}\s*\n\s*{', r'},\n{', cleaned)
            cleaned = re.sub(r']\s*\n\s*\[', r'],\n[', cleaned)

            # Remove extra whitespace and normalize
            lines = cleaned.split('\n')
            normalized_lines = []
            for line in lines:
                line = line.strip()
                if line:
                    normalized_lines.append(line)

            cleaned = '\n'.join(normalized_lines)

            return cleaned

        except Exception as e:
            logger.warning(f"Error cleaning JSON response: {e}")
            return response_text

    def _clean_font_references(self, mapped_slides: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Clean up any font_size, font_family, font_style references from AI response

        Args:
            mapped_slides: Slides from AI response

        Returns:
            List of cleaned slides without font references
        """
        try:
            cleaned_slides = []

            for slide in mapped_slides:
                cleaned_slide = {
                    "slideId": slide.get("slideId"),
                    "elements": []
                }

                for element in slide.get("elements", []):
                    cleaned_element = {
                        "objectId": element.get("objectId"),
                        "text": element.get("text"),
                        "Type": element.get("Type"),
                        "max_length": element.get("max_length")
                    }

                    # Remove any font-related properties that AI might have added
                    # Only keep the essential properties we need
                    cleaned_slide["elements"].append(cleaned_element)

                cleaned_slides.append(cleaned_slide)

            logger.info(f"Cleaned {len(cleaned_slides)} slides, removed font references")
            return cleaned_slides

        except Exception as e:
            logger.warning(f"Error cleaning font references: {e}")
            return mapped_slides  # Return original if cleaning fails


# Hàm để lấy singleton instance
def get_slide_generation_service() -> SlideGenerationService:
    """
    Lấy singleton instance của SlideGenerationService
    Thread-safe lazy initialization

    Returns:
        SlideGenerationService: Singleton instance
    """
    return SlideGenerationService()
